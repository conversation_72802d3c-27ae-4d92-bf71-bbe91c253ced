<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\UserController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes (no authentication required)
Route::prefix('auth')->group(function () {
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/login', [AuthController::class, 'login']);
});

// Alternative routes without auth prefix (for compatibility)
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Protected routes (authentication required)
Route::middleware('auth:sanctum')->group(function () {
    
    // Authentication routes
    Route::prefix('auth')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/user', [AuthController::class, 'user']);
    });
    
    // Alternative routes without auth prefix (for compatibility)
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'user']);
    Route::put('/profile', [AuthController::class, 'updateProfile']);
    
    // Notification routes
    Route::prefix('notifications')->group(function () {
        // Get received notifications
        Route::get('/', [NotificationController::class, 'index']);
        
        // Get sent notifications (admin only)
        Route::get('/sent', [NotificationController::class, 'sent']);
        
        // Send new notification (admin only)
        Route::post('/', [NotificationController::class, 'store']);
        
        // Mark notification as read
        Route::put('/{notificationId}/read', [NotificationController::class, 'markAsRead']);
        
        // Mark all notifications as read
        Route::put('/read-all', [NotificationController::class, 'markAllAsRead']);
        
        // Delete notification
        Route::delete('/{notificationId}', [NotificationController::class, 'destroy']);
        
        // Get notification statistics (admin only)
        Route::get('/stats', [NotificationController::class, 'stats']);
    });
    
    // User management routes (admin only)
    Route::prefix('users')->group(function () {
        // Get all users
        Route::get('/', [UserController::class, 'index']);
        
        // Get user by ID
        Route::get('/{userId}', [UserController::class, 'show']);
        
        // Update user status
        Route::put('/{userId}/status', [UserController::class, 'updateStatus']);
        
        // Get user statistics
        Route::get('/stats', [UserController::class, 'stats']);
    });
});

// Health check route
Route::get('/health', function () {
    return response()->json([
        'success' => true,
        'message' => 'API is working',
        'timestamp' => now(),
    ]);
});

// Fallback route for undefined API endpoints
Route::fallback(function () {
    return response()->json([
        'success' => false,
        'message' => 'API endpoint not found',
    ], 404);
});
