<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class NotificationController extends Controller
{
    /**
     * Get notifications for authenticated user.
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();
            
            // Get notifications for this user
            $notifications = Notification::forUser($user->id)
                ->with(['sender:id,name,user_type'])
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($notification) use ($user) {
                    // Get read status for this user
                    $pivot = $notification->recipients()
                        ->where('user_id', $user->id)
                        ->first()?->pivot;
                    
                    return [
                        'id' => $notification->id,
                        'title' => $notification->title,
                        'message' => $notification->message,
                        'type' => $notification->type,
                        'sender_id' => $notification->sender_id,
                        'sender_name' => $notification->sender->name ?? 'Unknown',
                        'sender_type' => $notification->sender->user_type ?? 'unknown',
                        'recipient_type' => $notification->recipient_type,
                        'specific_recipients' => $notification->specific_recipients,
                        'attachment_url' => $notification->attachment_url,
                        'attachment_name' => $notification->attachment_name,
                        'is_read' => $pivot?->is_read ?? false,
                        'read_at' => $pivot?->read_at,
                        'created_at' => $notification->created_at,
                        'updated_at' => $notification->updated_at,
                    ];
                });

            return response()->json([
                'success' => true,
                'notifications' => $notifications,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الإشعارات',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get sent notifications (admin only).
     */
    public function sent(Request $request)
    {
        try {
            $user = $request->user();
            
            if (!$user->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'غير مصرح لك بالوصول',
                ], 403);
            }

            $notifications = Notification::where('sender_id', $user->id)
                ->with(['sender:id,name,user_type'])
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($notification) {
                    return [
                        'id' => $notification->id,
                        'title' => $notification->title,
                        'message' => $notification->message,
                        'type' => $notification->type,
                        'sender_id' => $notification->sender_id,
                        'sender_name' => $notification->sender->name ?? 'Unknown',
                        'sender_type' => $notification->sender->user_type ?? 'unknown',
                        'recipient_type' => $notification->recipient_type,
                        'specific_recipients' => $notification->specific_recipients,
                        'attachment_url' => $notification->attachment_url,
                        'attachment_name' => $notification->attachment_name,
                        'created_at' => $notification->created_at,
                        'updated_at' => $notification->updated_at,
                    ];
                });

            return response()->json([
                'success' => true,
                'notifications' => $notifications,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الإشعارات المرسلة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Send a new notification (admin only).
     */
    public function store(Request $request)
    {
        try {
            $user = $request->user();
            
            if (!$user->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'غير مصرح لك بإرسال الإشعارات',
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'message' => 'required|string',
                'type' => 'sometimes|in:general,urgent,announcement,reminder',
                'recipient_type' => 'required|in:students,teachers,all,specific',
                'specific_recipients' => 'required_if:recipient_type,specific|array',
                'specific_recipients.*' => 'exists:users,id',
                'attachment_url' => 'nullable|url',
                'attachment_name' => 'nullable|string|max:255',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors(),
                ], 422);
            }

            DB::beginTransaction();

            // Create notification
            $notification = Notification::create([
                'title' => $request->title,
                'message' => $request->message,
                'type' => $request->type ?? 'general',
                'sender_id' => $user->id,
                'recipient_type' => $request->recipient_type,
                'specific_recipients' => $request->specific_recipients,
                'attachment_url' => $request->attachment_url,
                'attachment_name' => $request->attachment_name,
            ]);

            // Get target recipients
            $recipients = $notification->getTargetRecipients();

            // Create notification-user relationships
            $recipientData = $recipients->map(function ($recipient) use ($notification) {
                return [
                    'notification_id' => $notification->id,
                    'user_id' => $recipient->id,
                    'is_read' => false,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            })->toArray();

            if (!empty($recipientData)) {
                DB::table('notification_user')->insert($recipientData);
            }

            DB::commit();

            // Load sender relationship
            $notification->load('sender:id,name,user_type');

            return response()->json([
                'success' => true,
                'message' => 'تم إرسال الإشعار بنجاح',
                'notification' => [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'type' => $notification->type,
                    'sender_id' => $notification->sender_id,
                    'sender_name' => $notification->sender->name,
                    'sender_type' => $notification->sender->user_type,
                    'recipient_type' => $notification->recipient_type,
                    'specific_recipients' => $notification->specific_recipients,
                    'attachment_url' => $notification->attachment_url,
                    'attachment_name' => $notification->attachment_name,
                    'created_at' => $notification->created_at,
                    'updated_at' => $notification->updated_at,
                ],
                'recipients_count' => $recipients->count(),
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال الإشعار',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead(Request $request, $notificationId)
    {
        try {
            $user = $request->user();
            
            $pivot = DB::table('notification_user')
                ->where('notification_id', $notificationId)
                ->where('user_id', $user->id)
                ->first();

            if (!$pivot) {
                return response()->json([
                    'success' => false,
                    'message' => 'الإشعار غير موجود',
                ], 404);
            }

            DB::table('notification_user')
                ->where('notification_id', $notificationId)
                ->where('user_id', $user->id)
                ->update([
                    'is_read' => true,
                    'read_at' => now(),
                    'updated_at' => now(),
                ]);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث حالة الإشعار',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث حالة الإشعار',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead(Request $request)
    {
        try {
            $user = $request->user();
            
            DB::table('notification_user')
                ->where('user_id', $user->id)
                ->where('is_read', false)
                ->update([
                    'is_read' => true,
                    'read_at' => now(),
                    'updated_at' => now(),
                ]);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث جميع الإشعارات',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الإشعارات',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete notification.
     */
    public function destroy(Request $request, $notificationId)
    {
        try {
            $user = $request->user();
            
            if ($user->isAdmin()) {
                // Admin can delete any notification they sent
                $notification = Notification::where('id', $notificationId)
                    ->where('sender_id', $user->id)
                    ->first();
                
                if (!$notification) {
                    return response()->json([
                        'success' => false,
                        'message' => 'الإشعار غير موجود',
                    ], 404);
                }
                
                $notification->delete();
            } else {
                // Regular users can only remove notification from their list
                $deleted = DB::table('notification_user')
                    ->where('notification_id', $notificationId)
                    ->where('user_id', $user->id)
                    ->delete();
                
                if (!$deleted) {
                    return response()->json([
                        'success' => false,
                        'message' => 'الإشعار غير موجود',
                    ], 404);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الإشعار',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف الإشعار',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get notification statistics (admin only).
     */
    public function stats(Request $request)
    {
        try {
            $user = $request->user();
            
            if (!$user->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'غير مصرح لك بالوصول',
                ], 403);
            }

            $stats = [
                'total_notifications' => Notification::count(),
                'sent_by_me' => Notification::where('sender_id', $user->id)->count(),
                'total_users' => User::where('user_type', '!=', 'admin')->count(),
                'active_users' => User::active()->where('user_type', '!=', 'admin')->count(),
                'students_count' => User::active()->byType('student')->count(),
                'teachers_count' => User::active()->byType('teacher')->count(),
                'urgent_notifications' => Notification::urgent()->count(),
                'notifications_today' => Notification::whereDate('created_at', today())->count(),
                'notifications_this_week' => Notification::whereBetween('created_at', [
                    now()->startOfWeek(),
                    now()->endOfWeek()
                ])->count(),
                'notifications_this_month' => Notification::whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->count(),
            ];

            return response()->json([
                'success' => true,
                'stats' => $stats,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الإحصائيات',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
