# Complete NotiSend Setup Guide

## 🎯 What We've Built

A complete notification system with:
- **Flutter Mobile App** - Professional Arabic UI with role-based dashboards
- **Laravel API Backend** - RESTful API with authentication and notification management
- **MySQL Database** - Structured data storage for users and notifications

## 📱 Flutter App Features

### ✅ Completed Features:
- 🔐 **Authentication System** - Login/Register with role-based access
- 🎨 **Professional UI** - Green theme, Arabic support, Material Design 3
- 👥 **Role-Based Dashboards**:
  - **Admin**: Send notifications, view statistics, manage users
  - **Teacher**: Receive notifications, view profile
  - **Student**: Receive notifications, view profile
- 📢 **Notification Management**:
  - Send notifications (Admin only)
  - Receive and read notifications
  - Mark as read/unread
  - Search and filter notifications
  - Real-time unread count
- 👤 **Profile Management** - View and edit user information
- 💾 **Local Storage** - Offline data persistence
- 🔄 **State Management** - Using Provider pattern

## 🚀 Installation Steps

### Step 1: Install Flutter Dependencies

```bash
cd ntiapp
flutter pub get
```

### Step 2: Set Up Laravel Backend

1. **Copy <PERSON>vel <PERSON>** to `C:\laragon\www\notysend`:
   - Copy all files from `laravel_files/` to your Laravel project
   - Follow the detailed instructions in `laravel_files/INSTALLATION_GUIDE.md`

2. **Install Laravel Packages**:
   ```bash
   cd C:\laragon\www\notysend
   composer require laravel/sanctum
   composer require fruitcake/laravel-cors
   ```

3. **Configure Database**:
   - Create MySQL database named `notysend`
   - Update `.env` file with database credentials

4. **Run Migrations**:
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

### Step 3: Configure API URL

Update the API URL in `ntiapp/lib/services/api_service.dart`:

```dart
// For Laragon with virtual host
static const String baseUrl = 'http://notysend.test/api';

// Or for localhost
// static const String baseUrl = 'http://localhost/notysend/public/api';
```

### Step 4: Test the Application

1. **Start Laravel Server**:
   ```bash
   cd C:\laragon\www\notysend
   php artisan serve
   ```

2. **Run Flutter App**:
   ```bash
   cd ntiapp
   flutter run
   ```

## 🧪 Testing Guide

### Default Test Users

The Laravel seeder creates these users:

| Role | Email | Password | Purpose |
|------|-------|----------|---------|
| Admin | <EMAIL> | password | Send notifications, manage system |
| Teacher | <EMAIL> | password | Receive notifications |
| Student | <EMAIL> | password | Receive notifications |

### Test Scenarios

#### 1. Admin Workflow:
1. Login as admin
2. Navigate to "إرسال إشعار" tab
3. Create and send a notification to students/teachers
4. View sent notifications in "الإشعارات" tab
5. Check dashboard statistics

#### 2. Teacher/Student Workflow:
1. Login as teacher or student
2. View received notifications on dashboard
3. Navigate to "الإشعارات" tab to see all notifications
4. Mark notifications as read
5. Update profile information

#### 3. API Testing:

Test API endpoints using curl or Postman:

```bash
# Login
curl -X POST http://notysend.test/api/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# Send Notification (use token from login response)
curl -X POST http://notysend.test/api/notifications \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "title": "إشعار تجريبي",
    "message": "هذا إشعار تجريبي للاختبار",
    "recipient_type": "all",
    "type": "general"
  }'
```

## 🗂️ Project Structure

### Flutter App Structure:
```
ntiapp/lib/
├── main.dart                    # App entry point
├── models/                      # Data models
├── providers/                   # State management
├── services/                    # API services
├── utils/                       # Utilities and themes
└── screens/                     # UI screens
    ├── auth/                    # Login/Register
    ├── dashboard/               # Role-based dashboards
    ├── notifications/           # Notification management
    └── profile/                 # User profile
```

### Laravel API Structure:
```
laravel_files/
├── migrations/                  # Database migrations
├── models/                      # Eloquent models
├── controllers/                 # API controllers
├── routes/                      # API routes
├── config/                      # Configuration files
└── database/seeders/            # Database seeders
```

## 🔧 Troubleshooting

### Common Issues:

1. **Flutter Dependencies Error**:
   ```bash
   flutter clean
   flutter pub get
   ```

2. **API Connection Error**:
   - Check if Laravel server is running
   - Verify API URL in Flutter app
   - Check CORS configuration

3. **Database Connection Error**:
   - Verify MySQL is running in Laragon
   - Check database credentials in `.env`
   - Ensure database `notysend` exists

4. **Authentication Issues**:
   - Clear app data and try again
   - Check Sanctum configuration
   - Verify token generation

### Debug Commands:

```bash
# Flutter
flutter doctor
flutter analyze

# Laravel
php artisan route:list
php artisan config:clear
php artisan cache:clear
```

## 🚀 Next Steps

### Immediate Tasks:
1. Install and configure Laravel backend
2. Test API endpoints
3. Run Flutter app and test all features
4. Create additional test users if needed

### Future Enhancements:
- 📱 Push notifications
- 📎 File attachments
- 📊 Advanced analytics
- 🌐 Web admin panel
- 📧 Email notifications
- 🔔 Real-time notifications with WebSockets

## 📞 Support

If you encounter any issues:
1. Check the troubleshooting section
2. Verify all installation steps were followed
3. Test API endpoints independently
4. Check Laravel and Flutter logs for errors

## 🎉 Success Criteria

Your setup is successful when:
- ✅ Flutter app runs without errors
- ✅ Laravel API responds to requests
- ✅ You can login with test users
- ✅ Admin can send notifications
- ✅ Users can receive and read notifications
- ✅ All dashboards display correctly

The complete notification system is now ready for use! 🚀
