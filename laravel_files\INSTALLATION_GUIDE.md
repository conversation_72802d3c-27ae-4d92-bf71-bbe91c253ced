# Laravel API Installation Guide for NotiSend

## Step 1: Copy Files to Your Laravel Project

Copy the following files to your Laravel project at `C:\laragon\www\notysend`:

### 1. Database Migrations
Copy these files to `database/migrations/`:
- `2024_01_01_000001_create_users_table.php`
- `2024_01_01_000002_create_notifications_table.php`
- `2024_01_01_000003_create_notification_user_table.php`
- `2024_01_01_000004_create_personal_access_tokens_table.php`

### 2. Models
Copy these files to `app/Models/`:
- `User.php` (replace existing)
- `Notification.php`

### 3. Controllers
Create directory `app/Http/Controllers/Api/` and copy:
- `AuthController.php`
- `NotificationController.php`
- `UserController.php`

### 4. Routes
Replace `routes/api.php` with the provided file.

### 5. Configuration
Copy `config/cors.php` (replace existing if needed).

### 6. Database Seeder
Replace `database/seeders/DatabaseSeeder.php` with the provided file.

## Step 2: Install Required Packages

Open terminal in your Laravel project directory and run:

```bash
# Install Laravel Sanctum for API authentication
composer require laravel/sanctum

# Install CORS package if not already installed
composer require fruitcake/laravel-cors
```

## Step 3: Configure Environment

Edit your `.env` file:

```env
# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=notysend
DB_USERNAME=root
DB_PASSWORD=

# App Configuration
APP_NAME="NotiSend API"
APP_ENV=local
APP_KEY=base64:your-app-key-here
APP_DEBUG=true
APP_URL=http://notysend.test

# Sanctum Configuration
SANCTUM_STATEFUL_DOMAINS=localhost,127.0.0.1,notysend.test
SESSION_DRIVER=cookie
```

## Step 4: Publish Sanctum Configuration

```bash
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
```

## Step 5: Configure Sanctum

Edit `config/sanctum.php` and ensure these settings:

```php
'stateful' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', sprintf(
    '%s%s',
    'localhost,localhost:3000,127.0.0.1,127.0.0.1:8000,::1',
    Sanctum::currentApplicationUrlWithPort()
))),

'middleware' => [
    'verify_csrf_token' => App\Http\Middleware\VerifyCsrfToken::class,
    'encrypt_cookies' => App\Http\Middleware\EncryptCookies::class,
],
```

## Step 6: Update Kernel.php

Add Sanctum middleware to `app/Http/Kernel.php`:

```php
'api' => [
    \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
    'throttle:api',
    \Illuminate\Routing\Middleware\SubstituteBindings::class,
],
```

## Step 7: Run Migrations and Seeders

```bash
# Create database (if not exists)
# You can do this through phpMyAdmin or MySQL command line

# Run migrations
php artisan migrate

# Seed database with sample data
php artisan db:seed
```

## Step 8: Configure CORS (if needed)

If you encounter CORS issues, update `config/cors.php`:

```php
'paths' => ['api/*', 'sanctum/csrf-cookie'],
'allowed_methods' => ['*'],
'allowed_origins' => ['*'], // In production, specify your Flutter app domain
'allowed_headers' => ['*'],
'supports_credentials' => false,
```

## Step 9: Test API Endpoints

You can test the API using tools like Postman or curl:

### Register User:
```bash
curl -X POST http://notysend.test/api/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "password": "password",
    "password_confirmation": "password",
    "user_type": "student"
  }'
```

### Login:
```bash
curl -X POST http://notysend.test/api/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password"
  }'
```

## Step 10: Default Users Created

The seeder creates these default users:

1. **Admin**: <EMAIL> / password
2. **Teacher**: <EMAIL> / password
3. **Student**: <EMAIL> / password

## Step 11: Update Flutter App API URL

In your Flutter app, update `lib/services/api_service.dart`:

```dart
// For Laragon with virtual host
static const String baseUrl = 'http://notysend.test/api';

// Or for localhost
// static const String baseUrl = 'http://localhost/notysend/public/api';
```

## Troubleshooting

### Common Issues:

1. **CORS Errors**: Make sure CORS is properly configured
2. **404 Errors**: Ensure your web server is configured correctly
3. **Database Connection**: Check your `.env` database settings
4. **Token Issues**: Make sure Sanctum is properly installed and configured

### Laravel Commands for Debugging:

```bash
# Clear all caches
php artisan config:clear
php artisan cache:clear
php artisan route:clear

# Check routes
php artisan route:list

# Check database connection
php artisan tinker
>>> DB::connection()->getPdo();
```

## Production Considerations

For production deployment:

1. Set `APP_DEBUG=false` in `.env`
2. Configure proper CORS origins
3. Use HTTPS
4. Set up proper database credentials
5. Configure proper session and cache drivers
6. Set up proper logging

## API Documentation

### Authentication Endpoints:
- `POST /api/register` - Register new user
- `POST /api/login` - Login user
- `POST /api/logout` - Logout user (requires auth)
- `GET /api/user` - Get authenticated user (requires auth)
- `PUT /api/profile` - Update user profile (requires auth)

### Notification Endpoints:
- `GET /api/notifications` - Get received notifications (requires auth)
- `GET /api/notifications/sent` - Get sent notifications (admin only)
- `POST /api/notifications` - Send notification (admin only)
- `PUT /api/notifications/{id}/read` - Mark as read (requires auth)
- `PUT /api/notifications/read-all` - Mark all as read (requires auth)
- `DELETE /api/notifications/{id}` - Delete notification (requires auth)
- `GET /api/notifications/stats` - Get statistics (admin only)

### User Management Endpoints:
- `GET /api/users` - Get all users (admin only)
- `GET /api/users/{id}` - Get user by ID (admin only)
- `PUT /api/users/{id}/status` - Update user status (admin only)
- `GET /api/users/stats` - Get user statistics (admin only)
