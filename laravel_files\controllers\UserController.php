<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    /**
     * Get all users (admin only).
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();
            
            if (!$user->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'غير مصرح لك بالوصول',
                ], 403);
            }

            $query = User::query();

            // Filter by user type if provided
            if ($request->has('user_type') && $request->user_type) {
                $query->byType($request->user_type);
            }

            // Filter by active status
            if ($request->has('active')) {
                if ($request->active === 'true' || $request->active === '1') {
                    $query->active();
                } elseif ($request->active === 'false' || $request->active === '0') {
                    $query->where('is_active', false);
                }
            }

            // Search by name or email
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            }

            $users = $query->orderBy('created_at', 'desc')
                ->select(['id', 'name', 'email', 'user_type', 'phone', 'department', 'is_active', 'created_at', 'updated_at'])
                ->paginate($request->per_page ?? 15);

            return response()->json([
                'success' => true,
                'users' => $users,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب المستخدمين',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get user by ID (admin only).
     */
    public function show(Request $request, $userId)
    {
        try {
            $user = $request->user();
            
            if (!$user->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'غير مصرح لك بالوصول',
                ], 403);
            }

            $targetUser = User::find($userId);

            if (!$targetUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'المستخدم غير موجود',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'user' => $targetUser->only([
                    'id', 'name', 'email', 'user_type', 'phone', 
                    'department', 'is_active', 'created_at', 'updated_at'
                ]),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب بيانات المستخدم',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update user status (admin only).
     */
    public function updateStatus(Request $request, $userId)
    {
        try {
            $user = $request->user();
            
            if (!$user->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'غير مصرح لك بالوصول',
                ], 403);
            }

            $targetUser = User::find($userId);

            if (!$targetUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'المستخدم غير موجود',
                ], 404);
            }

            // Prevent admin from deactivating themselves
            if ($targetUser->id === $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكنك تعطيل حسابك الخاص',
                ], 422);
            }

            $targetUser->update([
                'is_active' => $request->boolean('is_active'),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث حالة المستخدم',
                'user' => $targetUser->only([
                    'id', 'name', 'email', 'user_type', 'phone', 
                    'department', 'is_active', 'created_at', 'updated_at'
                ]),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث حالة المستخدم',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get user statistics (admin only).
     */
    public function stats(Request $request)
    {
        try {
            $user = $request->user();
            
            if (!$user->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'غير مصرح لك بالوصول',
                ], 403);
            }

            $stats = [
                'total_users' => User::count(),
                'active_users' => User::active()->count(),
                'inactive_users' => User::where('is_active', false)->count(),
                'admins_count' => User::byType('admin')->count(),
                'teachers_count' => User::byType('teacher')->count(),
                'students_count' => User::byType('student')->count(),
                'users_today' => User::whereDate('created_at', today())->count(),
                'users_this_week' => User::whereBetween('created_at', [
                    now()->startOfWeek(),
                    now()->endOfWeek()
                ])->count(),
                'users_this_month' => User::whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->count(),
            ];

            return response()->json([
                'success' => true,
                'stats' => $stats,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الإحصائيات',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
