<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'admin',
            'phone' => '+966501234567',
            'department' => 'إدارة النظام',
            'is_active' => true,
        ]);

        // Create sample teacher
        User::create([
            'name' => 'أحمد محمد',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'teacher',
            'phone' => '+966507654321',
            'department' => 'الرياضيات',
            'is_active' => true,
        ]);

        // Create sample student
        User::create([
            'name' => 'فاطمة علي',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'student',
            'phone' => '+966509876543',
            'department' => 'الصف الثالث الثانوي',
            'is_active' => true,
        ]);

        // Create additional sample users
        User::create([
            'name' => 'سارة أحمد',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'teacher',
            'phone' => '+966502345678',
            'department' => 'اللغة العربية',
            'is_active' => true,
        ]);

        User::create([
            'name' => 'محمد خالد',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'student',
            'phone' => '+966508765432',
            'department' => 'الصف الثاني الثانوي',
            'is_active' => true,
        ]);

        User::create([
            'name' => 'نورا سعد',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'student',
            'phone' => '+966503456789',
            'department' => 'الصف الأول الثانوي',
            'is_active' => true,
        ]);
    }
}
