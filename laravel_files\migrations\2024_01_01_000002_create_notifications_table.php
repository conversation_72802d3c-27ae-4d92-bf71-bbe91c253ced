<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('message');
            $table->enum('type', ['general', 'urgent', 'announcement', 'reminder'])->default('general');
            $table->foreignId('sender_id')->constrained('users')->onDelete('cascade');
            $table->enum('recipient_type', ['students', 'teachers', 'all', 'specific'])->default('all');
            $table->json('specific_recipients')->nullable(); // Array of user IDs for specific recipients
            $table->string('attachment_url')->nullable();
            $table->string('attachment_name')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
