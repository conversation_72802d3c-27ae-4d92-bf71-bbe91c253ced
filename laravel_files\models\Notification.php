<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'message',
        'type',
        'sender_id',
        'recipient_type',
        'specific_recipients',
        'attachment_url',
        'attachment_name',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'specific_recipients' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user who sent this notification.
     */
    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    /**
     * Get the users who received this notification.
     */
    public function recipients()
    {
        return $this->belongsToMany(User::class, 'notification_user')
                    ->withPivot('is_read', 'read_at')
                    ->withTimestamps();
    }

    /**
     * Get notification type in Arabic.
     */
    public function getTypeArabicAttribute(): string
    {
        return match($this->type) {
            'general' => 'عام',
            'urgent' => 'عاجل',
            'announcement' => 'إعلان',
            'reminder' => 'تذكير',
            default => 'إشعار',
        };
    }

    /**
     * Get recipient type in Arabic.
     */
    public function getRecipientTypeArabicAttribute(): string
    {
        return match($this->recipient_type) {
            'students' => 'الطلاب',
            'teachers' => 'المعلمين',
            'all' => 'الجميع',
            'specific' => 'محدد',
            default => 'غير محدد',
        };
    }

    /**
     * Check if notification has attachment.
     */
    public function getHasAttachmentAttribute(): bool
    {
        return !empty($this->attachment_url);
    }

    /**
     * Check if notification is urgent.
     */
    public function getIsUrgentAttribute(): bool
    {
        return $this->type === 'urgent';
    }

    /**
     * Scope to get notifications by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get urgent notifications.
     */
    public function scopeUrgent($query)
    {
        return $query->where('type', 'urgent');
    }

    /**
     * Scope to get notifications by recipient type.
     */
    public function scopeByRecipientType($query, $recipientType)
    {
        return $query->where('recipient_type', $recipientType);
    }

    /**
     * Scope to get notifications for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where(function ($q) use ($userId) {
            $q->where('recipient_type', 'all')
              ->orWhere(function ($subQ) use ($userId) {
                  $subQ->where('recipient_type', 'specific')
                       ->whereJsonContains('specific_recipients', $userId);
              })
              ->orWhere(function ($subQ) use ($userId) {
                  $user = User::find($userId);
                  if ($user) {
                      if ($user->isStudent()) {
                          $subQ->where('recipient_type', 'students');
                      } elseif ($user->isTeacher()) {
                          $subQ->where('recipient_type', 'teachers');
                      }
                  }
              });
        });
    }

    /**
     * Get recipients based on recipient type.
     */
    public function getTargetRecipients()
    {
        switch ($this->recipient_type) {
            case 'all':
                return User::active()->where('user_type', '!=', 'admin')->get();
            case 'students':
                return User::active()->byType('student')->get();
            case 'teachers':
                return User::active()->byType('teacher')->get();
            case 'specific':
                return User::active()->whereIn('id', $this->specific_recipients ?? [])->get();
            default:
                return collect();
        }
    }
}
